import { Stripe, loadStripe } from "@stripe/stripe-js";
import { db } from "@/lib/firebase";
import {
  doc,
  collection,
  getDocs,
  deleteDoc,
  addDoc,
  onSnapshot,
  query,
  where,
  updateDoc,
  increment,
  getDoc,
  setDoc,
} from "firebase/firestore";
import { User, getAuth } from "firebase/auth";
import { useEffect, useState } from "react";
import { PLANS } from "./constants";
let stripePromise: Stripe | null;

const initializeStripe = async () => {
  if (!stripePromise) {
    stripePromise = await loadStripe(
      String(process.env.NEXT_PUBLIC_STRIPE_API_KEY_TEST)
    );
  }
  return stripePromise;
};

// Create a checkout session without storing subscription details immediately.
// The success_url now includes the priceId so we can later look up the plan.
export async function createCheckoutSession(userId: string, priceId: string) {
  const checkoutSessionRef = await addDoc(
    collection(doc(db, "users", userId), "checkout_sessions"),
    {
      price: priceId,
      success_url:
        window.location.origin + `/payment-success?priceId=${priceId}`,
      cancel_url: window.location.origin,
      createdAt: new Date(),
    }
  );

  // Listen for the sessionId update and then redirect to Stripe checkout.
  onSnapshot(checkoutSessionRef, async (snap) => {
    if (snap.exists()) {
      const data = snap.data();

      const { sessionId } = data;
      if (sessionId) {
        const stripe = await initializeStripe();
        stripe?.redirectToCheckout({ sessionId });
      } else {
        console.log("Session ID is missing");
      }
    } else {
      console.log("Error: Document does not exist");
    }
  });
}

// This function finds the matching plan (from your PLANS constant),
// deletes any existing subscription records, and stores the new subscription details.
export async function confirmSubscription(userId: string, priceId: string) {
  const selectedPlan = PLANS.find(
    (plan) => plan.priceId === priceId || plan.yearlyPriceId === priceId
  );

  if (!selectedPlan) {
    console.error("No matching plan found for priceId:", priceId);
    return;
  }

  // Reference to the user's subscribedPlan collection.
  const subscribedPlanRef = collection(
    doc(db, "users", userId),
    "subscribedPlan"
  );

  // Delete any existing subscription.
  const existingSubscriptions = await getDocs(subscribedPlanRef);
  existingSubscriptions.forEach(async (docSnapshot) => {
    await deleteDoc(docSnapshot.ref);
  });

  // Save new plan details in Firestore under 'subscribedPlan'.
  await addDoc(subscribedPlanRef, {
    planName: selectedPlan.name,
    planPrice: selectedPlan.price,
    planCredits: selectedPlan.credit,
    planImageCredits: selectedPlan.imageCredits,
    features: [
      selectedPlan.feature1,
      selectedPlan.feature2,
      selectedPlan.feature3,
      selectedPlan.feature4,
      selectedPlan.feature5,
    ],
    subscribedAt: new Date(),
  });
  return { success: true };
}

export async function isUserPremium(): Promise<boolean> {
  const auth = getAuth(); // Get the auth instance

  // Ensure user is authenticated
  const user = auth.currentUser;
  if (!user) return false;

  // Refresh the ID token to get the latest claims
  await user.getIdToken(true);

  // Fetch the decoded token containing claims
  const decodedToken = await user.getIdTokenResult();

  // Check if the user has the premium role
  return decodedToken?.claims?.stripeRole ? true : false;
}

export async function getUserStripeRole(): Promise<Object | null> {
  const auth = getAuth(); // Get the Firebase Auth instance
  const user = auth.currentUser;

  if (!user) {
    console.log("No authenticated user found.");
    return null;
  }

  // Refresh the token to ensure claims are up-to-date
  await user.getIdToken(true);

  // Retrieve decoded token claims
  const decodedToken = await user.getIdTokenResult();

  // Extract the stripeRole claim
  const stripeRole = decodedToken?.claims?.stripeRole || null;

  return stripeRole;
}

export function usePremiumStatus(user: any) {
  const [premiumStatus, setPremiumStatus] = useState<boolean>(false);

  useEffect(() => {
    async function checkPremiumStatus() {
      // Only check if user exists; otherwise, do nothing
      if (user) {
        const result = await isUserPremium();
        setPremiumStatus(result);
      } else {
        // Optionally, reset the premium status if no user exists
        setPremiumStatus(false);
      }
    }
    checkPremiumStatus();
  }, [user]);

  return premiumStatus;
}

/**
 * @param userId Firebase userId.
 * @param packId Astria Ai packId.
 * @param promptId Astria Ai promptId.
 * @param productName Name of the product to display on Stripe Checkout.
 * @description This Function will create checkout session for buying packs with a dynamic price.
 */
export async function packPaymentCheckoutSessions(
  userId: string,
  packId: string | number,
  promptId: string | number,
  productName: string
) {
  if (!userId || !packId) {
    return { success: false, message: "Missing required parameters" };
  }

  const priceId = process.env.NEXT_PUBLIC_ASTRIA_PACKS_PLAN as string;

  // Generate a unique transaction ID for this purchase session
  const transactionId = `${userId}_${packId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  const checkoutSessionRef = await addDoc(
    collection(db, "users", userId, "checkout_sessions"),
    {
      price: priceId,
      mode: "payment",
      quantity: 1,
      name: productName,
      success_url:
        window.location.origin +
        `/packs/payment-success?packId=${packId}&userId=${userId}&promptId=${promptId}&transactionId=${transactionId}&productName=${productName}`,
      cancel_url: window.location.origin,
      createdAt: new Date(),
      transactionId,
      metadata: {
        userId,
        packId,
        transactionId,
        productName,
      },
    }
  );

  onSnapshot(checkoutSessionRef, async (snap) => {
    if (snap.exists()) {
      const data = snap.data();
      const { sessionId, url } = data;
      console.log("Session Data:", data);

      if (sessionId) {
        const stripe = await initializeStripe();
        if (stripe) {
          stripe.redirectToCheckout({ sessionId });
        } else {
          console.error("Stripe not initialized.");
        }
      } else if (url) {
        window.location.href = url;
      } else {
        console.error(
          "Session ID or URL is missing in the snapshot data. Full data:",
          data
        );
      }
    } else {
      console.warn(
        "Checkout session document no longer exists for session ID:",
        checkoutSessionRef.id
      );
    }
  });
}

/**
 *
 * @param userId userid is required
 * @param packId packId is required
 * @param transactionId unique transaction ID to prevent duplicate confirmations for same purchase
 * @returns if doc is added then true else false
 */
export async function onPackPaymentConfirmation(
  userId: string,
  packId: string,
  transactionId: string
) {

  if (!userId || !packId || !transactionId) {
    return { success: false, message: "Missing required parameters" };
  }

  try {
    // First, check if this specific transaction has already been processed
    const packCollectionRef = collection(db, "packs");
    
    // Make sure the user is authenticated before proceeding
    const auth = getAuth();
    const currentUser = auth.currentUser;
    
    if (!currentUser) {
      return { 
        success: false, 
        message: "User not authenticated. Please sign in again." 
      };
    }
    
    const existingTransactionQuery = query(
      packCollectionRef,
      where("userId", "==", userId),
      where("packId", "==", packId),
      where("transactionId", "==", transactionId)
    );

    const existingTransactionSnapshot = await getDocs(existingTransactionQuery);

    if (!existingTransactionSnapshot.empty) {
      // This exact transaction has already been processed (page refresh scenario)
      const existingDoc = existingTransactionSnapshot.docs[0];
      console.log(
        `Transaction already processed for userId: ${userId}, packId: ${packId}, transactionId: ${transactionId}. Doc ID: ${existingDoc.id}`
      );

      return {
        success: true,
        packDocRef: existingDoc.ref,
        alreadyExists: true,
        isRefresh: true,
        message: "Transaction already processed (page refresh detected)",
      };
    }

    // Check how many times user has purchased this pack before
    const userPacksQuery = query(
      packCollectionRef,
      where("userId", "==", userId),
      where("packId", "==", packId)
    );

    const userPacksSnapshot = await getDocs(userPacksQuery);
    const purchaseCount = userPacksSnapshot.size + 1; // +1 for the current purchase

    // Create a new document for this transaction
    const newPackDocRef = await addDoc(packCollectionRef, {
      userId,
      packId,
      transactionId,
      imageCredits: 35, // Each pack gives 35 credits
      purchaseNumber: purchaseCount, // Track which purchase this is (1st, 2nd, 3rd, etc.)
      createdAt: new Date(),
      expire: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    });

    console.log(
      `New pack document created for userId: ${userId}, packId: ${packId}, transactionId: ${transactionId}. Doc ID: ${newPackDocRef.id}. Purchase #${purchaseCount}`
    );

    // Update user's total image credits (you might want to implement this)
    await updateUserImageCredits(userId, 35);

    return {
      success: true,
      packDocRef: newPackDocRef,
      alreadyExists: false,
      isRefresh: false,
      purchaseCount,
      creditsAdded: 35,
      message: `Pack purchase #${purchaseCount} confirmed successfully. 35 credits added.`,
    };
  } catch (error) {
    console.log("Error in onPackPaymentConfirmation:", error);
    return { success: false, message: (error as Error).message };
  }
}

/**
 * Check if a user already owns a specific pack
 * @param userId userid is required
 * @param packId packId is required
 * @returns boolean indicating if user owns the pack
 */
export async function checkUserOwnspack(
  userId: string,
  packId: string
): Promise<{ success: boolean; owns: boolean; message?: string }> {
  try {
    const packCollectionRef = collection(db, "packs");
    const userPackQuery = query(
      packCollectionRef,
      where("userId", "==", userId),
      where("packId", "==", packId)
    );

    const userPackSnapshot = await getDocs(userPackQuery);

    return {
      success: true,
      owns: !userPackSnapshot.empty,
      message: userPackSnapshot.empty
        ? "User does not own this pack"
        : "User owns this pack",
    };
  } catch (error) {
    console.error("Error checking pack ownership:", error);
    return {
      success: false,
      owns: false,
      message: (error as Error).message,
    };
  }
}

/**
 * Update user's total image credits
 * @param userId user ID
 * @param creditsToAdd number of credits to add
 */
async function updateUserImageCredits(userId: string, creditsToAdd: number) {
  try {
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      // User document exists, increment the credits
      await updateDoc(userDocRef, {
        imageCredits: increment(creditsToAdd),
        lastCreditUpdate: new Date(),
      });
    } else {
      // User document doesn't exist, create it with initial credits
      await setDoc(userDocRef, {
        imageCredits: creditsToAdd,
        lastCreditUpdate: new Date(),
        createdAt: new Date(),
      });
    }

    console.log(`Added ${creditsToAdd} image credits to user ${userId}`);
  } catch (error) {
    console.error("Error updating user image credits:", error);
    throw error;
  }
}

/**
 * Get user's total image credits
 * @param userId user ID
 * @returns user's current image credits
 */
export async function getUserImageCredits(
  userId: string
): Promise<{ success: boolean; credits: number; message?: string }> {
  try {
    const userDocRef = doc(db, "users", userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const credits = userData.imageCredits || 0;
      return {
        success: true,
        credits,
        message: `User has ${credits} image credits`,
      };
    } else {
      return {
        success: true,
        credits: 0,
        message: "User document not found, assuming 0 credits",
      };
    }
  } catch (error) {
    console.error("Error getting user image credits:", error);
    return {
      success: false,
      credits: 0,
      message: (error as Error).message,
    };
  }
}

/**
 * Get user's pack purchase history
 * @param userId user ID
 * @param packId optional pack ID to filter by specific pack
 * @returns array of pack purchases
 */
export async function getUserPackHistory(
  userId: string,
  packId?: string
): Promise<{
  success: boolean;
  purchases: any[];
  totalCredits: number;
  message?: string;
}> {
  try {
    const packCollectionRef = collection(db, "packs");
    let userPacksQuery;

    if (packId) {
      userPacksQuery = query(
        packCollectionRef,
        where("userId", "==", userId),
        where("packId", "==", packId)
      );
    } else {
      userPacksQuery = query(packCollectionRef, where("userId", "==", userId));
    }

    const userPacksSnapshot = await getDocs(userPacksQuery);
    const purchases: any[] = [];
    let totalCredits = 0;

    userPacksSnapshot.forEach((doc) => {
      const data = doc.data();
      purchases.push({
        id: doc.id,
        ...data,
      });
      totalCredits += data.imageCredits || 0;
    });

    return {
      success: true,
      purchases,
      totalCredits,
      message: `Found ${purchases.length} pack purchases with total ${totalCredits} credits`,
    };
  } catch (error) {
    console.error("Error getting user pack history:", error);
    return {
      success: false,
      purchases: [],
      totalCredits: 0,
      message: (error as Error).message,
    };
  }
}
